[project]
name = "gpt-oss"
description = "A collection of reference inference implementations for gpt-oss by OpenAI"

dependencies = [
  "openai-harmony",
  "tiktoken>=0.9.0",
  "aiohttp>=3.12.14",
  "chz>=0.3.0",
  "docker>=7.1.0",
  "fastapi>=0.116.1",
  "html2text>=2025.4.15",
  "lxml>=4.9.4",
  "pydantic>=2.11.7",
  "structlog>=25.4.0",
  "tenacity>=9.1.2",
  "uvicorn>=0.35.0",
  "requests>=2.31.0",
  "termcolor",
]
readme = "README.md"
requires-python = ">=3.12,<3.13"
version = "0.0.1"

[project.optional-dependencies]
triton = ["triton", "safetensors>=0.5.3", "torch>=2.7.0"]
torch = ["safetensors>=0.5.3", "torch>=2.7.0"]
metal = ["numpy", "tqdm", "safetensors", "torch"]
test = ["pytest>=8.4.1", "httpx>=0.28.1"]
eval = ["pandas", "numpy", "openai", "jinja2", "tqdm", "blobfile"]

[build-system]
requires = ["setuptools>=68"]
build-backend = "gpt_oss_build_backend.backend"
backend-path = ["_build"]

[tool.setuptools]
packages = ["gpt_oss"]

[tool.scikit-build]
cmake.source-dir = "." # pick up the root CMakeLists.txt
cmake.args = [
  "-DGPTOSS_BUILD_PYTHON=ON",
  "-DCMAKE_BUILD_TYPE=Release",
  "-DBUILD_SHARED_LIBS=OFF",
]
[tool.scikit-build.wheel]
packages = ["gpt_oss"] # copy the whole Python package tree
